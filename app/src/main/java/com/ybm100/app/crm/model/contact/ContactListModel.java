package com.ybm100.app.crm.model.contact;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.contact.ContactListApi;
import com.ybm100.app.crm.contract.contact.ContactListContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/24/2018 10:26
 */
public class ContactListModel extends BaseModel implements ContactListContract.IContactListModel {

    public static ContactListModel newInstance() {

        return new ContactListModel();
    }

    @Override
    public Observable<ContactListApi> getContactsWithMerchat(int pageNo, int pageSize, String keyword, String merchantId) {

        return RetrofitCreateHelper.createApi(ApiService.class).getContactsWithMerchant(pageNo, pageSize, merchantId)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<ContactListApi> getContacts(int pageNo, int pageSize, String keyword) {

        return RetrofitCreateHelper.createApi(ApiService.class).getContacts(pageNo, pageSize, keyword)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> delContact(String id) {
        return RetrofitCreateHelper.createApi(ApiService.class).delContact(id)
                .compose(RxHelper.rxSchedulerHelper());
    }
}