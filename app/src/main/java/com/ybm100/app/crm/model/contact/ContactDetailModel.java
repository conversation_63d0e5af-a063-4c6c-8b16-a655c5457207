package com.ybm100.app.crm.model.contact;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.contract.contact.ContactDetailContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/25/2018 14:02
 */
public class ContactDetailModel extends BaseModel implements ContactDetailContract.IContactDetailModel {

    public static ContactDetailModel newInstance() {

        return new ContactDetailModel();
    }

    @Override
    public Observable<RequestBaseBean> addContactTag(String id, String tag) {
        return RetrofitCreateHelper.createApi(ApiService.class).addTag(id, tag)
                .compose(RxHelper.rxSchedulerHelper());
    }
}