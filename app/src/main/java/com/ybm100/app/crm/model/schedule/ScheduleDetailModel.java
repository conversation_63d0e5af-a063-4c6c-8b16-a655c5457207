package com.ybm100.app.crm.model.schedule;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.schedule.ScheduleDetailBean;
import com.ybm100.app.crm.contract.schedule.ScheduleDetailContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/22/2018 19:17
 */
public class ScheduleDetailModel extends BaseModel implements ScheduleDetailContract.IScheduleDetailModel {

    public static ScheduleDetailModel newInstance() {
        return new ScheduleDetailModel();
    }

    @Override
    public Observable<RequestBaseBean<ScheduleDetailBean>> getScheduleDetail(String scheduleId) {
        return RetrofitCreateHelper.createApi(ApiService.class).getScheduleDetail290(scheduleId)
                .compose(RxHelper.rxSchedulerHelper());
    }
}