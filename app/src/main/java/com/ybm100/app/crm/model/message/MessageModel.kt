package com.ybm100.app.crm.model.message

import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.bean.message.MessageApiBean
import com.ybm100.app.crm.bean.message.MessageType
import com.ybm100.app.crm.bean.message.SessionMSGBean
import com.ybm100.app.crm.contract.message.MessageContract
import com.ybm100.app.crm.model.CommonModel
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.ui.fragment.message.MSGNotificationFragment
import io.reactivex.Observable

/**
 * Created by XyyMvpSportTemplate on 12/21/2018 17:25
 */
class MessageModel : CommonModel(), MessageContract.IMessageModel, MessageContract.ISessionMessageModel {
    override fun getMsg(type: Int, pageNo: Int, pageSize: Int): Observable<RequestBaseBean<MessageApiBean>> {
        return if (type == MSGNotificationFragment.SHARE_) {
            RetrofitCreateHelper.createApi(ApiService::class.java).getShareMsg(pageNo, pageSize)
                    .compose(RxHelper.rxSchedulerHelper())
        } else if (type == MSGNotificationFragment.PURCHASER_) {
            RetrofitCreateHelper.createApi(ApiService::class.java).getMissionMsg(pageNo, pageSize)
                    .compose(RxHelper.rxSchedulerHelper())
        } else {
            RetrofitCreateHelper.createApi(ApiService::class.java).getLicenseMsg(pageNo, pageSize)
                    .compose(RxHelper.rxSchedulerHelper())
        }
    }

    override fun getAllMessageType(): Observable<RequestBaseBean<MessageType>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).allMessageType
                .compose(RxHelper.rxSchedulerHelper())
    }

    override fun read(type: Int, id: Long): Observable<RequestBaseBean<*>> {
        return if (type == MSGNotificationFragment.SHARE_) {
            RetrofitCreateHelper.createApi(ApiService::class.java).readDynamicMessage(id)
                    .compose(RxHelper.rxSchedulerHelper())
        } else if (type == MSGNotificationFragment.PURCHASER_) {
            RetrofitCreateHelper.createApi(ApiService::class.java).readTask(id)
                    .compose(RxHelper.rxSchedulerHelper())
        } else {
            RetrofitCreateHelper.createApi(ApiService::class.java).readLicence(id)
                    .compose(RxHelper.rxSchedulerHelper())
        }
    }

    override fun getSessionMSGList(pageNo: Int, limit: Int): Observable<RequestBaseBean<SessionMSGBean>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getSessionMSGList(pageNo, limit).compose(RxHelper.rxSchedulerHelper())
    }

    companion object {
        @JvmStatic
        fun newInstance(): MessageModel {
            return MessageModel()
        }
    }
}