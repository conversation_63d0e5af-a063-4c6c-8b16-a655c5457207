package com.ybm100.app.crm.model.contact;

import com.ybm100.app.crm.schedule.model.ContactJobEnum;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @file ContactConstants.java
 * @brief
 * @date 2018/12/26
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class ContactConstants {
    public static final String SPLIT_TAG = "、";
    public static LinkedHashMap<String, String> contactJobs = new LinkedHashMap<>();
    public static int MALE = 1;
    public static int FEMALE = 0;

    static {
        contactJobs.put("Boss", "老板");
        contactJobs.put("LegalPerson", "法人");
        contactJobs.put("ShopManager", "店长");
        contactJobs.put("Handler", "经理人");
        contactJobs.put("Landlady", "老板娘");
        contactJobs.put("Buyer", "采购");
        contactJobs.put("Pharmacist", "药师");
        contactJobs.put("Doctor", "医生");
        contactJobs.put("Cashier", "收银员");
        contactJobs.put("ShopAssistant", "店员");
        contactJobs.put("Other", "其他");
    }

    public static List<String> getContactJobNameList() {
        return new ArrayList<>(contactJobs.values());
    }


    public static void setContactJobs(List<ContactJobEnum> list) {
        if (list == null || list.size() == 0) {
            return;
        }
        contactJobs.clear();
        for (int i = 0; i < list.size(); i++) {
            ContactJobEnum contactJobEnum = list.get(i);
            if (contactJobEnum != null) {
                contactJobs.put(contactJobEnum.getContactJob(), contactJobEnum.getContactJobName());
            }
        }
    }
}
