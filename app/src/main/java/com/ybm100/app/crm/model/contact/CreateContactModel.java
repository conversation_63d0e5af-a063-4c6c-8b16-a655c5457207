package com.ybm100.app.crm.model.contact;

import android.text.TextUtils;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.utilslibrary.utils.TimeUtils;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.contact.QueryMerchantsApi;
import com.ybm100.app.crm.contract.contact.CreateContactContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;
import com.ybm100.app.crm.schedule.api.ScheduleApiService;
import com.ybm100.app.crm.schedule.model.ContactJobEnum;
import com.ybm100.app.crm.utils.SharedPrefManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

import static com.ybm100.app.crm.model.contact.ContactConstants.SPLIT_TAG;

/**
 * Created by XyyMvpSportTemplate on 12/24/2018 19:46
 */
public class CreateContactModel extends BaseModel implements CreateContactContract.ICreateContactModel {

    public static CreateContactModel newInstance() {

        return new CreateContactModel();
    }

    @Override
    public Observable<RequestBaseBean<List<ContactJobEnum>>> getContactJob() {
        return RetrofitCreateHelper.createApi(ScheduleApiService.class).getContactJobEnum()
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> addContactTag(String id, String tag) {
        return RetrofitCreateHelper.createApi(ApiService.class).addTag(id, tag)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<ContactBean>> updateContact(ContactBean contact, boolean create, String tag) {
        String sysUserId = SharedPrefManager.getInstance().getUserInfo().getSysUserId();
        Map<String, Object> fieldMap = new HashMap<>();

        if (!TextUtils.isEmpty(contact.getContactBirth())) {
            try {
                String contactBirth = contact.getContactBirth();
                long l = Long.parseLong(contactBirth);
                String date = TimeUtils.millis2String(l, TimeUtils.DATE_FORMAT_YMD);
                fieldMap.put("contactBirth", date);
            } catch (Exception e) {
                e.printStackTrace();
                fieldMap.put("contactBirth", contact.getContactBirth());
            }
        }
        fieldMap.put("contactJob", contact.getContactJob());
        if (!TextUtils.isEmpty(contact.getContactMobile())) {
            fieldMap.put("contactMobile", contact.getContactMobile());
        }
        if (!TextUtils.isEmpty(contact.getFixedLine())) {
            fieldMap.put("fixedLine", contact.getFixedLine());
        }
        fieldMap.put("contactName", contact.getContactName());
        fieldMap.put("contactSex", contact.getContactSex());

        fieldMap.put("creator", sysUserId);
        if (!create) {
            fieldMap.put("id", contact.getId());
        }
        fieldMap.put("merchantId", contact.getMerchantId());
        String tags = contact.getContactTag();
        if (!TextUtils.isEmpty(tags)) {
            if (!TextUtils.isEmpty(tag)) {
                tags = tags + SPLIT_TAG + tag;
            }
            fieldMap.put("contactTag", tags);
        } else {
            if (!TextUtils.isEmpty(tag)) {
                fieldMap.put("contactTag", tag);
            }
        }

        if (create) {
            return RetrofitCreateHelper.createApi(ApiService.class).addContact(fieldMap)
                    .compose(RxHelper.rxSchedulerHelper());
        } else {
            return RetrofitCreateHelper.createApi(ApiService.class).updateContact(fieldMap)
                    .compose(RxHelper.rxSchedulerHelper());
        }
    }

    @Override
    public Observable<QueryMerchantsApi> getDrugInfo(String obj) {
        return RetrofitCreateHelper.createApi(ApiService.class).getDrugInfo(obj)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<ContactBean>> delTag(ContactBean contact, boolean create) {
        String sysUserId = SharedPrefManager.getInstance().getUserInfo().getSysUserId();
        Map<String, Object> fieldMap = new HashMap<>();

        if (!TextUtils.isEmpty(contact.getContactBirth())) {
            try {
                String contactBirth = contact.getContactBirth();
                long l = Long.parseLong(contactBirth);
                String date = TimeUtils.millis2String(l, TimeUtils.DATE_FORMAT_YMD);
                fieldMap.put("contactBirth", date);
            } catch (Exception e) {
                e.printStackTrace();
                fieldMap.put("contactBirth", contact.getContactBirth());
            }
        }
        fieldMap.put("contactJob", contact.getContactJob());
        fieldMap.put("contactMobile", contact.getContactMobile());
        fieldMap.put("contactName", contact.getContactName());
        fieldMap.put("contactSex", contact.getContactSex());

        fieldMap.put("creator", sysUserId);
        if (!create) {
            fieldMap.put("id", contact.getId());
        }
        fieldMap.put("merchantId", contact.getMerchantId());
        String tags = contact.getContactTag();
        if (!TextUtils.isEmpty(tags)) {
            fieldMap.put("contactTag", tags);
        } else {
            fieldMap.put("contactTag", "");
        }

        if (create) {
            return RetrofitCreateHelper.createApi(ApiService.class).addContact(fieldMap)
                    .compose(RxHelper.rxSchedulerHelper());
        } else {
            return RetrofitCreateHelper.createApi(ApiService.class).updateContact(fieldMap)
                    .compose(RxHelper.rxSchedulerHelper());
        }
    }


}