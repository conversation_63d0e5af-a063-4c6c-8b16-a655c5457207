package com.ybm100.app.crm.model.share;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.share.BranchBean;
import com.ybm100.app.crm.contract.share.ShareRootContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 03/05/2019 19:07
 */
public class ShareRootModel extends BaseModel implements ShareRootContract.IShareRootModel {

    public static ShareRootModel newInstance() {
        return new ShareRootModel();
    }

    @Override
    public Observable<RequestBaseBean<List<BranchBean>>> getTopGroups() {
        return RetrofitCreateHelper.createApi(ApiService.class).getTopGroups()
                .compose(RxHelper.rxSchedulerHelper());
    }
}