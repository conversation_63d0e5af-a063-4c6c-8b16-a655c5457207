package com.ybm100.app.crm.model.share;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.share.ShareListBean;
import com.ybm100.app.crm.contract.share.ShareListContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 03/05/2019 19:07
 */
public class ShareListModel extends BaseModel implements ShareListContract.IShareListModel {

    public static ShareListModel newInstance() {
        return new ShareListModel();
    }

    @Override
    public Observable<RequestBaseBean<ShareListBean>> getListData(Map<String, String> params) {
        return RetrofitCreateHelper.createApi(ApiService.class).getShareListData(params)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> shareTimesAddOne(String sourceId) {
        return RetrofitCreateHelper.createApi(ApiService.class).shareTimesAddOne(sourceId)
                .compose(RxHelper.rxSchedulerHelper());
    }
}