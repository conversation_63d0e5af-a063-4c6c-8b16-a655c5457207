package com.ybm100.app.crm.model.license

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.LicenseService
import com.ybm100.app.crm.bean.license.DeliveryAddressBean
import com.ybm100.app.crm.contract.license.LicenseSelectAreaContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import io.reactivex.Observable

/**
 * @author: zcj
 * @time:2020/7/1.
 * Description:
 */
class LicenseSelectAreaModel : BaseModel(), LicenseSelectAreaContract.ILicenseSelectAreaModel {

    override fun getAddress(areaCode: String?): Observable<RequestBaseBean<List<DeliveryAddressBean>>> {
        return RetrofitCreateHelper.createApi(LicenseService::class.java).getAddress(areaCode)
                .compose(RxHelper.rxSchedulerHelper())
    }

}